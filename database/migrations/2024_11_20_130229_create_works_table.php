<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('works', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->string('category');
            $table->string('title'); // Title
            $table->string('title_extended')->default(''); // Extended title
            $table->integer('season')->nullable(); // Extended original title
            $table->integer('episode')->nullable(); // Extended original title
            $table->year('year_production')->nullable(); // Production year
            $table->year('year_published')->nullable(); // Published year
            $table->float('duration_min')->nullable(); // Duration in time format (e.g., HH:MM:SS)
            $table->timestamps(); // Created_at and updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('works');
    }
};
