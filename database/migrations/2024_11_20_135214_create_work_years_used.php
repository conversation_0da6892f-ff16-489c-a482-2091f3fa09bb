<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_years_used', function (Blueprint $table) {
            $table->id();
            $table->foreignId('work_id')->constrained('works'); // Foreign key to works table
            $table->integer('year'); // Member ID
            $table->timestamps();

            $table->unique(['work_id', 'year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_years_used');
    }
};
