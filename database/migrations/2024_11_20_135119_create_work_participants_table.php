<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_participants', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->foreignId('work_id')->constrained('works'); // Foreign key to works table
            $table->unsignedBigInteger('member_id'); // Member ID
            $table->integer('role_group_id'); // Role ID
            $table->integer('role_id'); // Role ID
            $table->string('role_name'); // Role Name
            $table->string('full_name'); // Full Name
            $table->string('appeared_as'); // Appeared As
            $table->timestamps(); // Created_at and updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_participants');
    }
};
