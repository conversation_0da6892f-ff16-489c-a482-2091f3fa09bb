<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('playlist_works', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('bcwork_id');
            $table->string('category')->index();
            $table->string('title')->index(); // Title
            $table->string('title_extended')->default('')->index(); // Extended title
            $table->integer('season')->nullable(); // Extended original title
            $table->integer('episode')->nullable(); // Extended original title
            $table->year('year_production')->nullable()->index(); // Production year
            $table->year('year_published')->nullable()->index(); // Published year
            $table->float('duration_min')->nullable(); // Duration in time format (e.g., HH:MM:SS)
            $table->string('directors')->default('')->index();
            $table->year('playlist_year')->index();
            $table->timestamps();

            $table->unique(['bcwork_id', 'playlist_year']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('playlist');
    }
};
