<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Scout\Searchable;

class Work extends Model
{
    use HasFactory;
    use Searchable;

    public function searchableAs()
    {
        return 'repertoire_works';
    }

    public function getScoutKey()
    {
        return $this->id;

    }
    public function filterableAttributes()
    {
        return ['duration_min', 'year_created', 'year_published'];
    }

    public function participations()
    {
        return $this->hasMany(Participant::class, 'work_id', 'id');
    }

    public function titles()
    {
        return $this->hasMany(Title::class, 'work_id', 'id');
    }

    public function years_used()
    {
        return $this->hasMany(WorkUsage::class, 'work_id', 'id');
    }

    /**
     * Accessor to get all director names.
     *
     * @return array
     */
    public function getDirectorNamesAttribute()
    {
        return $this->participations()
            ->where('role_id', 105)
            ->pluck('full_name')
            ->toArray();
    }
}
