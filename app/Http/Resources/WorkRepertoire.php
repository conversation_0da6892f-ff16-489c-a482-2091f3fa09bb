<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkRepertoire extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $array = parent::toArray($request);

        $array['directors'] = $this->participations
            ->filter(function ($participation) {
                return $participation->role_id == 105;
            })
            ->map(function ($filteredParticipation) {
                return [
                    'name' => $filteredParticipation->full_name
                ];
            })->values(); // Reset the index;

        $array['coauthors'] = $this->participations
            ->filter(function ($participation) {
                return $participation->role_group_id == 1 && $participation->role_id !== 105;
            })
            ->map(function ($filteredParticipation) {
                return [
                    'name' => $filteredParticipation->full_name,
                    'role' => $filteredParticipation->role_name,
                ];
            })->values(); // Reset the index;

        $array['performers'] = $this->participations
            ->filter(function ($participation) {
                return $participation->role_group_id == 3;
            })
            ->map(function ($filteredParticipation) {
                return [
                    'name' => $filteredParticipation->full_name,
                    'appeared_as' => $filteredParticipation->appeared_as,
                ];
            })->values(); // Reset the index;

        $array['producers'] = $this->participations
            ->filter(function ($participation) {
                return $participation->role_group_id == 4;
            })
            ->map(function ($filteredParticipation) {
                return [
                    'name' => $filteredParticipation->full_name
                ];
            })
            ->unique('name') // Ensure 'name' is unique
            ->values(); // Reset the index


        $array['titles'] = WorkTitle::collection($this->titles);

        unset($array['participations']);
        unset($array['created_at']);
        unset($array['updated_at']);


        return $array;
    }
}
