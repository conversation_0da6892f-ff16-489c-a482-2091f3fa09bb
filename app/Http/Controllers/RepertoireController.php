<?php

namespace App\Http\Controllers;

use App\Http\Resources\WorkRepertoire;
use App\Models\Work;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Meilisearch\Endpoints\Indexes;

class RepertoireController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);  // Default to 10 if not provided

        $search = cleanString($request->get('search') ?? '');
        $director = cleanString($request->get('director') ?? '');

        $search = cleanString($search);

        if(!empty($search))
        $query = Work::search($search, function (Indexes $meilisearch, string $query, array $options) use ($request, $director) {
            $options['filter'] = [];
            $options['attributesToRetrieve'] = ['*'];


            /*
                            $options['attributesToSearchOn'] = $director
                                ? ['director_names']
                                : ['title', 'title_extended', 'titles'];*/

            //$options['attributesToSearchOn'] = ['titles', 'title', 'title_extended'];


            if ($request->has('year') && $request->input('year') > 1800) {
                $options['filter'][] = "( year_published = {$request->year}  OR year_created = {$request->year})";
            }/*
            if ($request->has('director')) {
                $options['filter'][] = "( director_names = '{$director}')";
            }*/

            return $meilisearch->search($query, $options);
        })
            ->query(function ($query) use ($request, $director) {
                $query->with(['participations' => function ($query) use($director, $request) {
                    $query->whereNotIn('member_id', [2147483638, 2147483617, 2147483616]);

                }])->with('titles');

                // Check if the year parameter exists and is greater than 1800
                /*if ($request->has('year') && $request->input('year') > 1800) {
                    $year = $request->input('year');
                    $query->where('year_production', $year)
                        ->orWhere('year_published', $year);
                }*
*/
                // Check if the director parameter exists
                if ($request->has('director')) {
                    $query->whereHas('participations', function ($q) use ($director) {
                        $q->where('role_id', 105)
                            ->where('full_name', 'like', '%' . trim($director) . '%')
                        ;
                    });
                }
            })
            ->orderBy('title')
            ->orderBy('title_extended')
            ->orderBy('season')
            ->orderBy('episode');


        else {
            $query = Work::query()->with(['participations' => function ($query) use ($director, $request) {
                $query->whereNotIn('member_id', [2147483638, 2147483617, 2147483616]);
            }])->with('titles');

            if ($request->has('director')) {
                $query->whereHas('participations', function ($q) use ($director) {
                    $q->where('role_id', 105)
                        ->where('full_name', 'like', '%' . trim($director) . '%')
                    ;
                });
            }

            if ($request->has('year') && $request->input('year') > 1800) {
                $year = $request->input('year');
                $query->where( function ($query) use ($year) {
                    $query->where('year_production', $year)
                        ->orWhere('year_published', $year);
                });

            }

            $query->orderBy('title')
                ->orderBy('title_extended')
                ->orderBy('season')
                ->orderBy('episode');
        }




        $works = $query->paginate($perPage)
            ->appends($request->only(['search', 'year', 'per_page']));

        return WorkRepertoire::collection($works);

/*
        $works = Work::query()
            ->with('participations', function ($query) {
                $query->whereNotIn('member_id', [2147483638, 2147483617, 2147483616]);
            })
            ->with('titles')
            ->where( function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                    ->orWhere('title_extended', 'like', '%' . $request->search . '%')
                ->orWhereHas('titles', function ($query) use ($request) {

                            $query->where('title', 'like', '%' . $request->search . '%')
                            ->orWhere('title_extended', 'like', '%' . $request->search . '%')
                            ;
                    })
                    ->orWhereHas('titles', function ($query) use ($request) {
                        $query->where('title', 'like', '%' . $request->search . '%');
                    });
            })
        //  ->where('title', 'like', '%' . $request->search . '%')

            ->orderBy('title')
            ->orderBy('title_extended')
            ->orderBy('season')
            ->orderBy('episode')

            ->paginate($perPage)
            ->appends(['search' => $request->search])
        ;
*/


        return WorkRepertoire::collection($works);




    }
}
