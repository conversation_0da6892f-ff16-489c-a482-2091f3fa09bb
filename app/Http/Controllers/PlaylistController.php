<?php

namespace App\Http\Controllers;

use App\Http\Resources\WorkRepertoire;
use App\Models\PlaylistWork;
use App\Models\Work;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Meilisearch\Endpoints\Indexes;

class PlaylistController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);  // Default to 10 if not provided

        $search = cleanString($request->get('search') ?? '');
        $director = cleanString($request->get('director') ?? '');

        $query = PlaylistWork::query()
            ->with('titles');

        if($request->has('search'))
            $query->where( function ($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search . '%')->orWhere('title_extended', 'like', '%' . $request->search . '%');
            });


        if($request->has('director'))
            $query->where('directors', 'like', '%' . $request->director . '%');

        if($request->has('playlist_year'))
            $query->where('playlist_year', $request->playlist_year);

        // Filter by unclaimed field if parameter is present
        if($request->has('unclaimed'))
            $query->where('unclaimed', 1);

        $query->orderBy('title')
        ->orderBy('title_extended')
        ->orderBy('season')
        ->orderBy('episode');

        return $query->paginate($perPage)
            ->appends($request->only(['search', 'director', 'playlist_year', 'unclaimed', 'per_page']));






    }
}
